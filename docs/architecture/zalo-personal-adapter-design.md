# Zalo Personal Adapter - Technical Design Document

## 1. Overview

This document outlines the technical design for the Zalo Personal Adapter, which will integrate personal Zalo accounts into the Omnichannel Inbox System using the unofficial zca-js library.

## 2. Requirements

### 2.1 Functional Requirements
- Connect to personal Zalo accounts via zca-js library
- Receive messages from Zalo personal accounts and store in database
- Send messages from agents to customers on Zalo personal accounts
- Synchronize customer information from Zalo personal accounts
- Support both Zalo personal (zca-js) and Zalo OA (official API) accounts

### 2.2 Non-Functional Requirements
- Handle rate limiting from Zalo to prevent account blocking
- Encrypt personal Zalo account credentials in database
- Handle different message types (text, image, attachment)
- Implement fallback mechanism if zca-js library stops working
- Secure handling of personal account information

## 3. Architecture

### 3.1 Component Diagram
```
                    ┌─────────────────────────────┐
                    │   Zalo Personal Adapter     │
                    │                             │
                    │  ┌──────────────────────┐   │
                    │  │  ZaloClient (zca-js) │   │
                    │  └──────────────────────┘   │
                    │                             │
                    │  ┌──────────────────────┐   │
                    │  │ Message Handler      │   │
                    │  └──────────────────────┘   │
                    │                             │
                    │  ┌──────────────────────┐   │
                    │  │ Customer Sync        │   │
                    │  └──────────────────────┘   │
                    └─────────────┬───────────────┘
                                  │
                    ┌─────────────▼───────────────┐
                    │    Channel Manager          │
                    └─────────────┬───────────────┘
                                  │
                    ┌─────────────▼───────────────┐
                    │        Database             │
                    │                             │
                    │  ┌──────────────────────┐   │
                    │  │ ChannelConfigs       │   │
                    │  │ (Encrypted)          │   │
                    │  └──────────────────────┘   │
                    │                             │
                    │  ┌──────────────────────┐   │
                    │  │ Conversations        │   │
                    │  └──────────────────────┘   │
                    │                             │
                    │  ┌──────────────────────┐   │
                    │  │ Messages             │   │
                    │  └──────────────────────┘   │
                    │                             │
                    │  ┌──────────────────────┐   │
                    │  │ Customers            │   │
                    │  └──────────────────────┘   │
                    └─────────────────────────────┘
```

### 3.2 Class Diagram
```
            ┌────────────────────────────────────┐
            │      ChannelAdapter (interface)    │
            │------------------------------------│
            │ + initialize(): Promise<void>      │
            │ + sendMessage(message: Message):   │
            │   Promise<void>                    │
            │ + receiveMessage(): Promise<void>  │
            │ + syncCustomerInfo():              │
            │   Promise<void>                    │
            └─────────────────▲──────────────────┘
                              │
    ┌─────────────────────────┴─────────────────────────┐
    │          ZaloPersonalAdapter                      │
    │---------------------------------------------------│
    │ - zaloClient: ZaloClient                          │
    │ - config: ZaloPersonalConfig                      │
    │ - rateLimiter: RateLimiter                        │
    │---------------------------------------------------│
    │ + initialize(): Promise<void>                     │
    │ + sendMessage(message: Message): Promise<void>    │
    │ + receiveMessage(): Promise<void>                 │
    │ + syncCustomerInfo(): Promise<void>               │
    │ + handleRateLimit(): void                         │
    │ + encryptCredentials(): string                    │
    │ + decryptCredentials(): ZaloCredentials           │
    │ + handleLibraryError(error: Error): void          │
    └───────────────────────────────────────────────────┘

            ┌────────────────────────────────────┐
            │      ZaloPersonalConfig            │
            │------------------------------------│
            │ + accountId: string                │
            │ + encryptedCredentials: string     │
            │ + isActive: boolean                │
            │ + rateLimitConfig: RateLimitConfig │
            └────────────────────────────────────┘
```

## 4. Implementation Details

### 4.1 ZaloPersonalAdapter Class

#### 4.1.1 Properties
- `zaloClient`: Instance of the zca-js client
- `config`: Configuration for the Zalo personal account
- `rateLimiter`: Rate limiting mechanism to prevent account blocking

#### 4.1.2 Methods

##### initialize()
- Initialize the zca-js client with decrypted credentials
- Set up event listeners for incoming messages
- Configure rate limiting

##### sendMessage(message: Message): Promise<void>
- Format message according to Zalo personal API requirements
- Send message using zca-js client
- Handle different message types (text, image, attachment)
- Update message status in database

##### receiveMessage(): Promise<void>
- Listen for incoming messages from zca-js client
- Parse message data
- Store message in database
- Trigger real-time updates

##### syncCustomerInfo(): Promise<void>
- Retrieve customer information from Zalo
- Update customer records in database
- Handle customer profile changes

##### handleRateLimit(): void
- Implement backoff strategy when rate limit is approached
- Log rate limiting events
- Notify system of potential issues

##### encryptCredentials(): string
- Encrypt Zalo account credentials before storing in database
- Use strong encryption algorithm

##### decryptCredentials(): ZaloCredentials
- Decrypt Zalo account credentials for use with zca-js client
- Handle decryption errors

##### handleLibraryError(error: Error): void
- Handle errors from zca-js library
- Implement fallback mechanisms
- Log errors for debugging

### 4.2 Database Schema Updates

#### 4.2.1 ChannelConfigs Collection
```javascript
{
  _id: ObjectId,
  channel: "zalo-personal",
  config: {
    accountId: String,
    encryptedCredentials: String, // Encrypted Zalo account credentials
    rateLimitConfig: {
      maxRequests: Number,
      timeWindow: Number // in seconds
    }
  },
  isActive: Boolean,
  createdAt: Date,
  updatedAt: Date
}
```

### 4.3 Security Considerations

#### 4.3.1 Credential Encryption
- Use AES-256 encryption for storing Zalo account credentials
- Generate encryption keys using secure random generation
- Store encryption keys securely (environment variables, AWS Secrets Manager, etc.)

#### 4.3.2 Rate Limiting
- Implement token bucket or leaky bucket algorithm
- Monitor API usage to prevent account blocking
- Log rate limiting events for analysis

### 4.4 Error Handling

#### 4.4.1 Library Errors
- Handle connection errors
- Handle authentication errors
- Handle message sending/receiving errors
- Implement retry mechanisms with exponential backoff

#### 4.4.2 Fallback Mechanisms
- Disable adapter temporarily if repeated errors occur
- Notify system administrators of issues
- Log detailed error information for debugging

## 5. Integration with Existing System

### 5.1 Channel Manager
The ChannelManager will be updated to:
- Load and initialize ZaloPersonalAdapter
- Route messages to the appropriate adapter based on channel type
- Manage adapter lifecycle (start, stop, restart)

### 5.2 Message Processing
- Incoming messages from Zalo personal accounts will be processed through the existing messaging pipeline
- Outgoing messages to Zalo personal accounts will be routed through the ZaloPersonalAdapter
- Message status updates will be handled consistently across all channels

### 5.3 Customer Synchronization
- Customer information from Zalo personal accounts will be synchronized with the existing customer database
- Customer profile updates will be handled through the existing customer management system

## 6. API Endpoints

### 6.1 Configuration Endpoints
- `POST /api/channels/zalo-personal/config`: Create Zalo personal configuration
- `PUT /api/channels/zalo-personal/config/:id`: Update Zalo personal configuration
- `DELETE /api/channels/zalo-personal/config/:id`: Delete Zalo personal configuration
- `GET /api/channels/zalo-personal/config`: List Zalo personal configurations
- `POST /api/channels/zalo-personal/test`: Test Zalo Personal account connection
- `POST /api/channels/zalo-personal/genLoginQR`: Initiates the QR code login flow. Returns a `requestId` and a `base64` encoded QR code image.
- `GET /api/channels/zalo-personal/checkLoginStatus/:requestId`: Checks the status of the QR code login initiated by `genLoginQR`. Returns `pending`, `success` (with tokens), or `expired`.

## 7. Testing Strategy

### 7.1 Unit Tests
- Test ZaloPersonalAdapter methods in isolation
- Mock zca-js client for testing
- Test encryption/decryption functions
- Test rate limiting implementation

### 7.2 Integration Tests
- Test integration with ChannelManager
- Test message sending/receiving with mock Zalo API
- Test customer synchronization
- Test database operations

### 7.3 End-to-End Tests
- Test complete message flow from agent to customer and back
- Test customer information synchronization
- Test rate limiting scenarios
- Test error handling and fallback mechanisms

## 8. Deployment Considerations

### 8.1 Environment Variables
- `ZALO_ENCRYPTION_KEY`: Key for encrypting Zalo credentials
- `ZALO_RATE_LIMIT_MAX`: Maximum requests per time window
- `ZALO_RATE_LIMIT_WINDOW`: Time window for rate limiting (in seconds)

### 8.2 Monitoring
- Monitor adapter initialization success/failure
- Monitor message sending/receiving success rates
- Monitor rate limiting events
- Monitor library errors and fallback activations

## 9. Future Enhancements

### 9.1 Enhanced Error Handling
- Implement more sophisticated fallback mechanisms
- Add predictive rate limiting based on historical data

### 9.2 Performance Improvements
- Implement message batching for better efficiency
- Add caching for frequently accessed customer information

### 9.3 Additional Features
- Support for Zalo personal account linking
- Enhanced customer profile synchronization
- Support for Zalo personal account management through the UI