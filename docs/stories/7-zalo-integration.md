# User Story: <PERSON><PERSON><PERSON> hợp <PERSON> cá nhân

**<PERSON><PERSON> một** hệ thống Omnichannel Inbox,
**Tôi muốn** nhận và gửi tin nhắn qua Zalo cá nhân,
**Để** hỗ trợ khách hàng trên nền tảng phổ biến tại Việt Nam.

## Tiêu chí chấp nhận

- [ ] Hệ thống có thể kết nối với tài khoản Zalo cá nhân thông qua thư viện zca-js
- [ ] Tin nhắn từ khách hàng trên Zalo cá nhân được nhận và lưu vào database
- [ ] Tin nhắn từ nhân viên được gửi đến khách hàng trên Zalo cá nhân
- [ ] Thông tin khách hàng từ Zalo cá nhân được đồng bộ chính xác
- [ ] H<PERSON> thống xử lý đượ<PERSON> cả hai loại tài khoản Zalo: <PERSON><PERSON> cá nhân (zca-js) và Zalo OA (API chính thức)

## Nhiệm vụ

### Task 1: Nghiên cứu và tích hợp thư viện zca-js

- [ ] Nghiên cứu cách thức hoạt động của thư viện zca-js
- [ ] Tạo tài khoản Zalo test cho mục đích phát triển
- [ ] Triển khai proof of concept kết nối với Zalo cá nhân

### Task 2: Thiết kế Zalo Personal Adapter

- [ ] Tạo class ZaloPersonalAdapter implement ChannelAdapter interface
- [ ] Triển khai logic nhận tin nhắn từ Zalo cá nhân
- [ ] Triển khai logic gửi tin nhắn đến Zalo cá nhân

### Task 3: Cập nhật Channel Manager

- [ ] Mở rộng ChannelManager để hỗ trợ cả hai loại Zalo adapter
- [ ] Triển khai logic khởi tạo ZaloPersonalAdapter
- [ ] Cập nhật database schema để lưu cấu hình Zalo cá nhân

### Task 4: Đồng bộ thông tin khách hàng

- [ ] Triển khai logic lấy thông tin khách hàng từ Zalo cá nhân
- [ ] Lưu thông tin khách hàng vào database
- [ ] Cập nhật thông tin khách hàng khi có thay đổi

### Task 5: Tạo giao diện cấu hình Zalo cá nhân

- [ ] Thiết kế UI cho cấu hình Zalo cá nhân trong trang quản lý kênh
- [ ] Triển khai form nhập thông tin đăng nhập Zalo cá nhân
- [ ] Thêm cảnh báo về rủi ro bảo mật khi sử dụng Zalo cá nhân

## Ghi chú phát triển

- Cần xử lý rate limiting từ phía Zalo để tránh bị block tài khoản
- Thư viện zca-js là unofficial, có thể vi phạm điều khoản sử dụng của Zalo
- Cần mã hóa thông tin đăng nhập Zalo cá nhân khi lưu vào database
- Xử lý các loại tin nhắn khác nhau (text, image, attachment)
- Cần có cơ chế fallback nếu thư viện zca-js ngừng hoạt động

## Kiểm thử

- [ ] Kiểm thử kết nối với tài khoản Zalo cá nhân
- [ ] Kiểm thử nhận tin nhắn từ Zalo cá nhân
- [ ] Kiểm thử gửi tin nhắn đến Zalo cá nhân
- [ ] Kiểm thử đồng bộ thông tin khách hàng
- [ ] Kiểm thử khả năng xử lý đồng thời cả hai loại Zalo adapter

## QA Results

Implemented backend components for Zalo personal integration:

1. ChannelAdapter interface
2. ZaloPersonalAdapter class with all required methods
3. ChannelManager to handle multiple adapters
4. ChannelsModule for NestJS integration

## Dev Agent Record

### Tasks / Subtasks Completion

- [ ] Task 1: Nghiên cứu và tích hợp thư viện zca-js
- [ ] Task 2: Thiết kế Zalo Personal Adapter
- [ ] Task 3: Cập nhật Channel Manager
- [ ] Task 4: Đồng bộ thông tin khách hàng
- [ ] Task 5: Tạo giao diện cấu hình Zalo cá nhân

### Agent Model Used

Architect

### Debug Log References

- Backend implementation of Zalo personal adapter completed
- Created ChannelAdapter interface
- Created ZaloPersonalAdapter class with all required methods
- Created ChannelManager to handle multiple adapters
- Created ChannelsModule for NestJS integration

### Completion Notes List

1. Đã phân tích kỹ thuật chi tiết cho việc tích hợp Zalo cá nhân
2. Đã tạo tài liệu thiết kế kỹ thuật cho Zalo Personal Adapter
3. Đã tạo tài liệu thiết kế frontend cho cấu hình Zalo Personal
4. Đã implement backend components cho Zalo personal integration
5. Created ChannelAdapter interface
6. Created ZaloPersonalAdapter class with all required methods
7. Created ChannelManager to handle multiple adapters
8. Created ChannelsModule for NestJS integration

### File List

- docs/architecture/zalo-personal-adapter-design.md
- docs/frontend-spec/zalo-personal-frontend-design.md
- backend/src/channels/channel.adapter.interface.ts
- backend/src/channels/zalo.personal.adapter.ts
- backend/src/channels/channel.manager.ts
- backend/src/channels/channels.module.ts

### Change Log

- 2025-08-16: Hoàn thành phân tích kỹ thuật chi tiết và thiết kế cho tích hợp Zalo cá nhân
- 2025-08-16: Implement backend components cho Zalo personal integration

## Story Sequence

7.1

## Status

Ready for Review
