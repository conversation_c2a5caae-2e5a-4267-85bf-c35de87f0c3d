import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Message, MessageDocument } from './schemas/message.schema';
import { ConversationsService } from '../conversations/conversations.service';

@Injectable()
export class MessagesService {
  constructor(
    @InjectModel(Message.name) private messageModel: Model<MessageDocument>,
    private conversationsService: ConversationsService,
  ) {}

  async create(createMessageDto: any): Promise<Message> {
    const createdMessage = new this.messageModel(createMessageDto);
    const savedMessage = await createdMessage.save();

    // Update conversation with last message info
    if (savedMessage.conversationId) {
      await this.conversationsService.updateLastMessage(
        savedMessage.conversationId.toString(),
        savedMessage.content.substring(0, 100),
      );
    }

    return savedMessage;
  }

  async findByConversation(conversationId: string, limit: number = 50, offset: number = 0): Promise<Message[]> {
    return this.messageModel
      .find({ conversationId, isDeleted: false })
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(offset)
      .populate('agentId', 'firstName lastName email')
      .exec();
  }

  async findOne(id: string): Promise<Message> {
    return this.messageModel
      .findById(id)
      .populate('agentId', 'firstName lastName email')
      .exec();
  }

  async findByChannelMessageId(channel: string, channelMessageId: string): Promise<Message> {
    return this.messageModel
      .findOne({ channel, channelMessageId })
      .exec();
  }

  async update(id: string, updateMessageDto: any): Promise<Message> {
    return this.messageModel
      .findByIdAndUpdate(id, updateMessageDto, { new: true })
      .populate('agentId', 'firstName lastName email')
      .exec();
  }

  async updateStatus(id: string, status: string): Promise<Message> {
    const updateData: any = { status };
    
    if (status === 'delivered') {
      updateData.deliveredAt = new Date();
    } else if (status === 'read') {
      updateData.readAt = new Date();
    }

    return this.messageModel
      .findByIdAndUpdate(id, updateData, { new: true })
      .exec();
  }

  async markAsDeleted(id: string): Promise<Message> {
    return this.messageModel
      .findByIdAndUpdate(
        id,
        { isDeleted: true, deletedAt: new Date() },
        { new: true },
      )
      .exec();
  }

  async remove(id: string): Promise<Message> {
    return this.messageModel.findByIdAndDelete(id).exec();
  }
}
