import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type MessageDocument = Message & Document;

@Schema({ timestamps: true })
export class Message {
  @Prop({ type: Types.ObjectId, ref: 'Conversation', required: true })
  conversationId: Types.ObjectId;

  @Prop({ required: true })
  messageId: string;

  @Prop({ required: true, enum: ['text', 'image', 'file', 'audio', 'video', 'sticker', 'location'] })
  type: string;

  @Prop({ required: true })
  content: string;

  @Prop({ required: true, enum: ['incoming', 'outgoing'] })
  direction: string;

  @Prop({ required: true })
  senderId: string;

  @Prop({ required: true })
  senderName: string;

  @Prop({ enum: ['customer', 'agent', 'system'] })
  senderType: string;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  agentId: Types.ObjectId;

  @Prop({ required: true })
  channel: string;

  @Prop()
  channelMessageId: string;

  @Prop({ enum: ['sent', 'delivered', 'read', 'failed'], default: 'sent' })
  status: string;

  @Prop()
  sentAt: Date;

  @Prop()
  deliveredAt: Date;

  @Prop()
  readAt: Date;

  @Prop([{
    type: { type: String, enum: ['image', 'file', 'audio', 'video'] },
    url: String,
    filename: String,
    size: Number,
    mimeType: String,
  }])
  attachments: Array<{
    type: string;
    url: string;
    filename: string;
    size: number;
    mimeType: string;
  }>;

  @Prop({ type: Object })
  channelData: Record<string, any>;

  @Prop({ type: Object })
  metadata: Record<string, any>;

  @Prop()
  replyToMessageId: string;

  @Prop({ default: false })
  isEdited: boolean;

  @Prop()
  editedAt: Date;

  @Prop({ default: false })
  isDeleted: boolean;

  @Prop()
  deletedAt: Date;
}

export const MessageSchema = SchemaFactory.createForClass(Message);

// Create indexes for better query performance
MessageSchema.index({ conversationId: 1, createdAt: -1 });
MessageSchema.index({ channel: 1, channelMessageId: 1 });
MessageSchema.index({ senderId: 1 });
MessageSchema.index({ agentId: 1 });
MessageSchema.index({ status: 1 });
