import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ChannelConfig, ChannelConfigSchema } from './schemas/channel-config.schema';
import { ChannelManager } from './channel.manager';
import { ChannelsService } from './channels.service';
import { ChannelsController } from './channels.controller';
import { ZaloController } from './zalo.controller';
import { MessageSyncService } from './message-sync.service';
import { ConversationsModule } from '../conversations/conversations.module';
import { MessagesModule } from '../messages/messages.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: ChannelConfig.name, schema: ChannelConfigSchema }]),
    ConversationsModule,
    MessagesModule,
  ],
  controllers: [ChannelsController, ZaloController],
  providers: [ChannelManager, ChannelsService, MessageSyncService],
  exports: [ChannelManager, ChannelsService, MessageSyncService],
})
export class ChannelsModule {}
