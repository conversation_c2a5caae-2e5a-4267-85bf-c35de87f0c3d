import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type ChannelConfigDocument = ChannelConfig & Document;

@Schema({ timestamps: true })
export class ChannelConfig {
  @Prop({ required: true, enum: ['facebook', 'zalo-personal', 'zalo-oa', 'email', 'sms'] })
  channel: string;

  @Prop({ required: true })
  name: string;

  @Prop()
  description: string;

  @Prop({ type: Object, required: true })
  config: Record<string, any>;

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ type: Object })
  rateLimitConfig: {
    maxRequests: number;
    timeWindow: number; // in seconds
  };

  @Prop({ type: Object })
  webhookConfig: {
    url: string;
    secret: string;
    events: string[];
  };

  @Prop({ type: Object })
  metadata: Record<string, any>;
}

export const ChannelConfigSchema = SchemaFactory.createForClass(ChannelConfig);

// Create indexes
ChannelConfigSchema.index({ channel: 1 });
ChannelConfigSchema.index({ isActive: 1 });
