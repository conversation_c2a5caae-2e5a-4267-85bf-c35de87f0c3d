import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ChannelConfig, ChannelConfigDocument } from './schemas/channel-config.schema';
import { ChannelManager } from './channel.manager';

@Injectable()
export class ChannelsService {
  constructor(
    @InjectModel(ChannelConfig.name) private channelConfigModel: Model<ChannelConfigDocument>,
    private channelManager: ChannelManager,
  ) {}

  async create(createChannelDto: any): Promise<ChannelConfig> {
    const createdChannel = new this.channelConfigModel(createChannelDto);
    const savedChannel = await createdChannel.save();
    
    // Initialize adapter if channel is active
    if (savedChannel.isActive) {
      await this.channelManager.addChannel(savedChannel);
    }
    
    return savedChannel;
  }

  async findAll(): Promise<ChannelConfig[]> {
    return this.channelConfigModel.find().exec();
  }

  async findOne(id: string): Promise<ChannelConfig> {
    return this.channelConfigModel.findById(id).exec();
  }

  async findByChannel(channel: string): Promise<ChannelConfig[]> {
    return this.channelConfigModel.find({ channel }).exec();
  }

  async update(id: string, updateChannelDto: any): Promise<ChannelConfig> {
    const updatedChannel = await this.channelConfigModel
      .findByIdAndUpdate(id, updateChannelDto, { new: true })
      .exec();
    
    // Update adapter
    await this.channelManager.updateChannel(id, updateChannelDto);
    
    return updatedChannel;
  }

  async remove(id: string): Promise<ChannelConfig> {
    const deletedChannel = await this.channelConfigModel.findByIdAndDelete(id).exec();
    
    // Remove adapter
    await this.channelManager.removeChannel(id);
    
    return deletedChannel;
  }

  async getChannelHealth(): Promise<Record<string, boolean>> {
    return this.channelManager.getChannelHealth();
  }
}
