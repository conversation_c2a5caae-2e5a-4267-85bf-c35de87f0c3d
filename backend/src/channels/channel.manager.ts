import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ChannelAdapter, ChannelAdapterConfig, ChannelMessage, SendMessageOptions } from './interfaces/channel-adapter.interface';
import { ChannelConfig, ChannelConfigDocument } from './schemas/channel-config.schema';
import { ZaloPersonalAdapter } from './adapters/zalo-personal.adapter';

@Injectable()
export class ChannelManager {
  private readonly logger = new Logger(ChannelManager.name);
  private adapters: Map<string, ChannelAdapter> = new Map();

  constructor(
    @InjectModel(ChannelConfig.name) private channelConfigModel: Model<ChannelConfigDocument>,
  ) {}

  async initializeAdapters(): Promise<void> {
    this.logger.log('Initializing channel adapters...');
    
    const configs = await this.channelConfigModel.find({ isActive: true }).exec();
    
    for (const config of configs) {
      try {
        await this.createAdapter(config);
      } catch (error) {
        this.logger.error(`Failed to initialize adapter for ${config.channel}:`, error);
      }
    }
    
    this.logger.log(`Initialized ${this.adapters.size} channel adapters`);
  }

  private async createAdapter(config: ChannelConfigDocument): Promise<void> {
    const adapterConfig: ChannelAdapterConfig = {
      channelId: config._id.toString(),
      channelType: config.channel,
      credentials: config.config,
      rateLimitConfig: config.rateLimitConfig,
    };

    let adapter: ChannelAdapter;

    switch (config.channel) {
      case 'zalo-personal':
        adapter = new ZaloPersonalAdapter(adapterConfig);
        break;
      // Add other adapters here
      // case 'facebook':
      //   adapter = new FacebookAdapter(adapterConfig);
      //   break;
      // case 'zalo-oa':
      //   adapter = new ZaloOAAdapter(adapterConfig);
      //   break;
      default:
        throw new Error(`Unsupported channel type: ${config.channel}`);
    }

    await adapter.connect();
    this.adapters.set(config._id.toString(), adapter);
    
    this.logger.log(`Adapter for ${config.channel} (${config._id}) initialized successfully`);
  }

  async getAdapter(channelId: string): Promise<ChannelAdapter | undefined> {
    return this.adapters.get(channelId);
  }

  async getAllAdapters(): Promise<ChannelAdapter[]> {
    return Array.from(this.adapters.values());
  }

  async sendMessage(channelId: string, options: SendMessageOptions): Promise<ChannelMessage> {
    const adapter = this.adapters.get(channelId);
    if (!adapter) {
      throw new Error(`No adapter found for channel: ${channelId}`);
    }

    if (!adapter.isConnectedStatus()) {
      throw new Error(`Adapter for channel ${channelId} is not connected`);
    }

    return adapter.sendMessage(options);
  }

  async addChannel(channelConfig: Partial<ChannelConfig>): Promise<string> {
    const config = new this.channelConfigModel(channelConfig);
    const savedConfig = await config.save();
    
    if (savedConfig.isActive) {
      await this.createAdapter(savedConfig);
    }
    
    return savedConfig._id.toString();
  }

  async updateChannel(channelId: string, updates: Partial<ChannelConfig>): Promise<void> {
    await this.channelConfigModel.findByIdAndUpdate(channelId, updates).exec();
    
    // Restart adapter if it exists
    if (this.adapters.has(channelId)) {
      await this.removeAdapter(channelId);
      const updatedConfig = await this.channelConfigModel.findById(channelId).exec();
      if (updatedConfig && updatedConfig.isActive) {
        await this.createAdapter(updatedConfig);
      }
    }
  }

  async removeChannel(channelId: string): Promise<void> {
    await this.removeAdapter(channelId);
    await this.channelConfigModel.findByIdAndDelete(channelId).exec();
  }

  private async removeAdapter(channelId: string): Promise<void> {
    const adapter = this.adapters.get(channelId);
    if (adapter) {
      await adapter.disconnect();
      this.adapters.delete(channelId);
      this.logger.log(`Adapter for channel ${channelId} removed`);
    }
  }

  async handleWebhook(channelId: string, payload: any): Promise<void> {
    const adapter = this.adapters.get(channelId);
    if (!adapter) {
      throw new Error(`No adapter found for channel: ${channelId}`);
    }

    await adapter.handleWebhook(payload);
  }

  async getChannelHealth(): Promise<Record<string, boolean>> {
    const health: Record<string, boolean> = {};
    
    for (const [channelId, adapter] of this.adapters) {
      try {
        health[channelId] = await adapter.isHealthy();
      } catch (error) {
        health[channelId] = false;
        this.logger.error(`Health check failed for channel ${channelId}:`, error);
      }
    }
    
    return health;
  }

  async shutdown(): Promise<void> {
    this.logger.log('Shutting down channel adapters...');
    
    const disconnectPromises = Array.from(this.adapters.values()).map(adapter => 
      adapter.disconnect().catch(error => 
        this.logger.error('Error disconnecting adapter:', error)
      )
    );
    
    await Promise.all(disconnectPromises);
    this.adapters.clear();
    
    this.logger.log('All channel adapters shut down');
  }
}
