import { <PERSON>, Get, Post, Body, Patch, Param, Delete, HttpException, HttpStatus } from '@nestjs/common';
import { ChannelsService } from './channels.service';
import { ChannelManager } from './channel.manager';
import { ZaloPersonalAdapter } from './adapters/zalo-personal.adapter';

@Controller('api/channels')
export class ChannelsController {
  constructor(
    private readonly channelsService: ChannelsService,
    private readonly channelManager: ChannelManager,
  ) {}

  @Post()
  create(@Body() createChannelDto: any) {
    return this.channelsService.create(createChannelDto);
  }

  @Get()
  findAll() {
    return this.channelsService.findAll();
  }

  @Get('health')
  async getHealth() {
    return this.channelsService.getChannelHealth();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.channelsService.findOne(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateChannelDto: any) {
    return this.channelsService.update(id, updateChannelDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.channelsService.remove(id);
  }

  // Zalo-specific endpoints
  @Post('zalo/loginQR')
  async generateZaloQR(@Body() body: { channelId?: string }) {
    try {
      let adapter: ZaloPersonalAdapter;
      
      if (body.channelId) {
        // Use existing channel
        const channelAdapter = await this.channelManager.getAdapter(body.channelId);
        if (!channelAdapter || !(channelAdapter instanceof ZaloPersonalAdapter)) {
          throw new HttpException('Invalid Zalo channel', HttpStatus.BAD_REQUEST);
        }
        adapter = channelAdapter;
      } else {
        // Create temporary adapter for QR generation
        adapter = new ZaloPersonalAdapter({
          channelId: 'temp',
          channelType: 'zalo-personal',
          credentials: {},
        });
        await adapter.connect();
      }

      const qrCodeData = await adapter.generateQRCode();
      
      return {
        success: true,
        qrCode: qrCodeData,
        message: 'QR code generated successfully. Please scan with your Zalo app.',
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to generate QR code',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post(':channelId/send-message')
  async sendMessage(
    @Param('channelId') channelId: string,
    @Body() body: {
      conversationId: string;
      content: string;
      type?: string;
      attachments?: any[];
    },
  ) {
    try {
      const message = await this.channelManager.sendMessage(channelId, {
        conversationId: body.conversationId,
        content: body.content,
        type: body.type as any,
        attachments: body.attachments,
      });

      return {
        success: true,
        message,
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to send message',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':channelId/conversations')
  async getConversations(@Param('channelId') channelId: string) {
    try {
      const adapter = await this.channelManager.getAdapter(channelId);
      if (!adapter) {
        throw new HttpException('Channel not found', HttpStatus.NOT_FOUND);
      }

      const conversations = await adapter.getConversations();
      
      return {
        success: true,
        conversations,
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to get conversations',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':channelId/conversations/:conversationId/messages')
  async getMessages(
    @Param('channelId') channelId: string,
    @Param('conversationId') conversationId: string,
  ) {
    try {
      const adapter = await this.channelManager.getAdapter(channelId);
      if (!adapter) {
        throw new HttpException('Channel not found', HttpStatus.NOT_FOUND);
      }

      const messages = await adapter.getMessages(conversationId);
      
      return {
        success: true,
        messages,
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to get messages',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post(':channelId/webhook')
  async handleWebhook(
    @Param('channelId') channelId: string,
    @Body() payload: any,
  ) {
    try {
      await this.channelManager.handleWebhook(channelId, payload);
      
      return {
        success: true,
        message: 'Webhook processed successfully',
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to process webhook',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
