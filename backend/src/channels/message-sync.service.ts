import { Injectable, Logger } from '@nestjs/common';
import { ConversationsService } from '../conversations/conversations.service';
import { MessagesService } from '../messages/messages.service';
import { ChannelMessage, ChannelConversation, ChannelContact } from './interfaces/channel-adapter.interface';

@Injectable()
export class MessageSyncService {
  private readonly logger = new Logger(MessageSyncService.name);

  constructor(
    private conversationsService: ConversationsService,
    private messagesService: MessagesService,
  ) {}

  async syncIncomingMessage(channelMessage: ChannelMessage, channelType: string): Promise<void> {
    try {
      // Find or create conversation
      let conversation = await this.conversationsService.findByChannelAndId(
        channelType,
        channelMessage.senderId,
      );

      if (!conversation) {
        // Create new conversation
        conversation = await this.conversationsService.create({
          customerId: channelMessage.senderId,
          customerName: channelMessage.senderName,
          channel: channelType,
          channelConversationId: channelMessage.senderId,
          status: 'open',
          priority: 'medium',
          lastMessageAt: channelMessage.timestamp,
          lastMessagePreview: channelMessage.content.substring(0, 100),
          unreadCount: 1,
          channelData: channelMessage.metadata,
        });

        this.logger.log(`Created new conversation for ${channelType}: ${conversation._id}`);
      }

      // Check if message already exists
      const existingMessage = await this.messagesService.findByChannelMessageId(
        channelType,
        channelMessage.id,
      );

      if (!existingMessage) {
        // Create new message
        const messageData = {
          conversationId: conversation._id,
          messageId: channelMessage.id,
          type: channelMessage.type,
          content: channelMessage.content,
          direction: 'incoming',
          senderId: channelMessage.senderId,
          senderName: channelMessage.senderName,
          senderType: 'customer',
          channel: channelType,
          channelMessageId: channelMessage.id,
          status: 'delivered',
          sentAt: channelMessage.timestamp,
          deliveredAt: new Date(),
          attachments: channelMessage.attachments || [],
          channelData: channelMessage.metadata,
        };

        await this.messagesService.create(messageData);
        this.logger.log(`Synced incoming message from ${channelType}: ${channelMessage.id}`);
      }
    } catch (error) {
      this.logger.error(`Failed to sync incoming message from ${channelType}:`, error);
      throw error;
    }
  }

  async syncOutgoingMessage(
    channelMessage: ChannelMessage,
    channelType: string,
    conversationId: string,
    agentId?: string,
  ): Promise<void> {
    try {
      // Check if message already exists
      const existingMessage = await this.messagesService.findByChannelMessageId(
        channelType,
        channelMessage.id,
      );

      if (!existingMessage) {
        // Create new message
        const messageData = {
          conversationId,
          messageId: channelMessage.id,
          type: channelMessage.type,
          content: channelMessage.content,
          direction: 'outgoing',
          senderId: agentId || 'system',
          senderName: channelMessage.senderName,
          senderType: 'agent',
          agentId,
          channel: channelType,
          channelMessageId: channelMessage.id,
          status: 'sent',
          sentAt: channelMessage.timestamp,
          attachments: channelMessage.attachments || [],
          channelData: channelMessage.metadata,
        };

        await this.messagesService.create(messageData);
        this.logger.log(`Synced outgoing message to ${channelType}: ${channelMessage.id}`);
      }
    } catch (error) {
      this.logger.error(`Failed to sync outgoing message to ${channelType}:`, error);
      throw error;
    }
  }

  async syncConversation(channelConversation: ChannelConversation, channelType: string): Promise<void> {
    try {
      let conversation = await this.conversationsService.findByChannelAndId(
        channelType,
        channelConversation.id,
      );

      const conversationData = {
        customerId: channelConversation.contact.id,
        customerName: channelConversation.contact.name,
        customerEmail: channelConversation.contact.email,
        customerPhone: channelConversation.contact.phone,
        customerAvatar: channelConversation.contact.avatar,
        channel: channelType,
        channelConversationId: channelConversation.id,
        unreadCount: channelConversation.unreadCount,
        channelData: channelConversation.metadata,
      };

      if (conversation) {
        // Update existing conversation
        await this.conversationsService.update(conversation._id.toString(), conversationData);
        this.logger.log(`Updated conversation for ${channelType}: ${conversation._id}`);
      } else {
        // Create new conversation
        conversationData['status'] = 'open';
        conversationData['priority'] = 'medium';
        
        if (channelConversation.lastMessage) {
          conversationData['lastMessageAt'] = channelConversation.lastMessage.timestamp;
          conversationData['lastMessagePreview'] = channelConversation.lastMessage.content.substring(0, 100);
        }

        conversation = await this.conversationsService.create(conversationData);
        this.logger.log(`Created new conversation for ${channelType}: ${conversation._id}`);
      }
    } catch (error) {
      this.logger.error(`Failed to sync conversation from ${channelType}:`, error);
      throw error;
    }
  }

  async syncContact(channelContact: ChannelContact, channelType: string): Promise<void> {
    try {
      // Update conversation with contact information
      const conversation = await this.conversationsService.findByChannelAndId(
        channelType,
        channelContact.id,
      );

      if (conversation) {
        const updateData = {
          customerName: channelContact.name,
          customerEmail: channelContact.email,
          customerPhone: channelContact.phone,
          customerAvatar: channelContact.avatar,
        };

        await this.conversationsService.update(conversation._id.toString(), updateData);
        this.logger.log(`Updated contact info for ${channelType}: ${channelContact.id}`);
      }
    } catch (error) {
      this.logger.error(`Failed to sync contact from ${channelType}:`, error);
      throw error;
    }
  }

  async updateMessageStatus(
    channelType: string,
    channelMessageId: string,
    status: 'sent' | 'delivered' | 'read' | 'failed',
  ): Promise<void> {
    try {
      const message = await this.messagesService.findByChannelMessageId(channelType, channelMessageId);
      
      if (message) {
        await this.messagesService.updateStatus(message._id.toString(), status);
        this.logger.log(`Updated message status for ${channelType}: ${channelMessageId} -> ${status}`);
      }
    } catch (error) {
      this.logger.error(`Failed to update message status for ${channelType}:`, error);
      throw error;
    }
  }

  async getConversationMessages(conversationId: string, limit: number = 50, offset: number = 0) {
    return this.messagesService.findByConversation(conversationId, limit, offset);
  }

  async markConversationAsRead(conversationId: string): Promise<void> {
    await this.conversationsService.markAsRead(conversationId);
  }
}
