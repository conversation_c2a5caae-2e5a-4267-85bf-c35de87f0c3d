export interface ChannelMessage {
  id: string;
  type: 'text' | 'image' | 'file' | 'audio' | 'video' | 'sticker' | 'location';
  content: string;
  senderId: string;
  senderName: string;
  timestamp: Date;
  attachments?: Array<{
    type: string;
    url: string;
    filename: string;
    size: number;
    mimeType: string;
  }>;
  metadata?: Record<string, any>;
}

export interface ChannelContact {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  avatar?: string;
  metadata?: Record<string, any>;
}

export interface ChannelConversation {
  id: string;
  contact: ChannelContact;
  lastMessage?: ChannelMessage;
  unreadCount: number;
  metadata?: Record<string, any>;
}

export interface SendMessageOptions {
  conversationId: string;
  content: string;
  type?: 'text' | 'image' | 'file' | 'audio' | 'video';
  attachments?: Array<{
    type: string;
    url: string;
    filename?: string;
  }>;
  replyToMessageId?: string;
  metadata?: Record<string, any>;
}

export interface ChannelAdapterConfig {
  channelId: string;
  channelType: string;
  credentials: Record<string, any>;
  webhookUrl?: string;
  rateLimitConfig?: {
    maxRequests: number;
    timeWindow: number;
  };
}

export abstract class ChannelAdapter {
  protected config: ChannelAdapterConfig;
  protected isConnected: boolean = false;

  constructor(config: ChannelAdapterConfig) {
    this.config = config;
  }

  // Connection management
  abstract connect(): Promise<void>;
  abstract disconnect(): Promise<void>;
  abstract isHealthy(): Promise<boolean>;

  // Authentication
  abstract authenticate(): Promise<boolean>;
  abstract refreshAuth(): Promise<boolean>;

  // Message operations
  abstract sendMessage(options: SendMessageOptions): Promise<ChannelMessage>;
  abstract receiveMessages(): Promise<ChannelMessage[]>;
  abstract markMessageAsRead(messageId: string): Promise<boolean>;

  // Conversation operations
  abstract getConversations(): Promise<ChannelConversation[]>;
  abstract getConversation(conversationId: string): Promise<ChannelConversation>;
  abstract getMessages(conversationId: string, limit?: number, offset?: number): Promise<ChannelMessage[]>;

  // Contact operations
  abstract getContact(contactId: string): Promise<ChannelContact>;
  abstract syncContacts(): Promise<ChannelContact[]>;

  // Webhook handling
  abstract handleWebhook(payload: any): Promise<void>;
  abstract validateWebhook(signature: string, payload: string): boolean;

  // Utility methods
  getChannelType(): string {
    return this.config.channelType;
  }

  getChannelId(): string {
    return this.config.channelId;
  }

  isConnectedStatus(): boolean {
    return this.isConnected;
  }

  // Event handlers (to be overridden by implementations)
  protected onMessageReceived(message: ChannelMessage): void {
    // Override in implementation
  }

  protected onMessageStatusChanged(messageId: string, status: string): void {
    // Override in implementation
  }

  protected onConnectionStatusChanged(isConnected: boolean): void {
    this.isConnected = isConnected;
  }

  protected onError(error: Error): void {
    console.error(`Channel ${this.config.channelType} error:`, error);
  }
}
