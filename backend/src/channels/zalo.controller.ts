import { Controller, Post, Body, HttpException, HttpStatus, Get, Param } from '@nestjs/common';
import { ChannelManager } from './channel.manager';
import { ZaloPersonalAdapter } from './adapters/zalo-personal.adapter';
import { ChannelsService } from './channels.service';

@Controller('api/zalo')
export class ZaloController {
  constructor(
    private readonly channelManager: ChannelManager,
    private readonly channelsService: ChannelsService,
  ) {}

  @Post('loginQR')
  async generateLoginQR(@Body() body: { channelId?: string }) {
    try {
      let adapter: ZaloPersonalAdapter;
      
      if (body.channelId) {
        // Use existing channel configuration
        const channelAdapter = await this.channelManager.getAdapter(body.channelId);
        if (!channelAdapter || !(channelAdapter instanceof ZaloPersonalAdapter)) {
          throw new HttpException('Invalid Zalo channel configuration', HttpStatus.BAD_REQUEST);
        }
        adapter = channelAdapter;
      } else {
        // Create temporary adapter for QR generation
        const tempConfig = {
          channelId: `temp-${Date.now()}`,
          channelType: 'zalo-personal',
          credentials: {},
        };
        
        adapter = new ZaloPersonalAdapter(tempConfig);
        await adapter.connect();
      }

      // Generate QR code for login
      const qrCodeBase64 = await adapter.generateQRCode();
      
      return {
        success: true,
        data: {
          qrCode: qrCodeBase64,
          expiresIn: 300, // 5 minutes
          instructions: 'Open Zalo app, go to Settings > QR Code Scanner, and scan this code to login',
        },
        message: 'QR code generated successfully',
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to generate QR code for Zalo login',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('configure')
  async configureZaloChannel(@Body() body: {
    name: string;
    description?: string;
    sessionData?: any;
    rateLimitConfig?: {
      maxRequests: number;
      timeWindow: number;
    };
  }) {
    try {
      const channelConfig = {
        channel: 'zalo-personal',
        name: body.name,
        description: body.description || 'Zalo Personal Account Integration',
        config: {
          sessionData: body.sessionData,
        },
        isActive: true,
        rateLimitConfig: body.rateLimitConfig || {
          maxRequests: 100,
          timeWindow: 3600, // 1 hour
        },
      };

      const createdChannel = await this.channelsService.create(channelConfig);
      
      return {
        success: true,
        data: {
          channelId: createdChannel._id,
          channel: createdChannel,
        },
        message: 'Zalo channel configured successfully',
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to configure Zalo channel',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('channels')
  async getZaloChannels() {
    try {
      const channels = await this.channelsService.findByChannel('zalo-personal');
      
      return {
        success: true,
        data: channels,
        message: 'Zalo channels retrieved successfully',
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to retrieve Zalo channels',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('channels/:channelId/status')
  async getChannelStatus(@Param('channelId') channelId: string) {
    try {
      const adapter = await this.channelManager.getAdapter(channelId);
      
      if (!adapter) {
        throw new HttpException('Channel not found', HttpStatus.NOT_FOUND);
      }

      const isHealthy = await adapter.isHealthy();
      const isConnected = adapter.isConnectedStatus();
      
      return {
        success: true,
        data: {
          channelId,
          isConnected,
          isHealthy,
          channelType: adapter.getChannelType(),
        },
        message: 'Channel status retrieved successfully',
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to get channel status',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('channels/:channelId/send')
  async sendMessage(
    @Param('channelId') channelId: string,
    @Body() body: {
      conversationId: string;
      content: string;
      type?: 'text' | 'image' | 'file' | 'audio' | 'video';
      attachments?: Array<{
        type: string;
        url: string;
        filename?: string;
      }>;
    },
  ) {
    try {
      const message = await this.channelManager.sendMessage(channelId, {
        conversationId: body.conversationId,
        content: body.content,
        type: body.type || 'text',
        attachments: body.attachments,
      });

      return {
        success: true,
        data: message,
        message: 'Message sent successfully',
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to send message',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('channels/:channelId/conversations')
  async getConversations(@Param('channelId') channelId: string) {
    try {
      const adapter = await this.channelManager.getAdapter(channelId);
      
      if (!adapter) {
        throw new HttpException('Channel not found', HttpStatus.NOT_FOUND);
      }

      const conversations = await adapter.getConversations();
      
      return {
        success: true,
        data: conversations,
        message: 'Conversations retrieved successfully',
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to retrieve conversations',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
