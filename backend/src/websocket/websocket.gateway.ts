import { Logger } from '@nestjs/common';
// import {
//   WebSocketGateway,
//   WebSocketServer,
//   SubscribeMessage,
//   OnGatewayConnection,
//   OnGatewayDisconnect,
//   MessageBody,
//   ConnectedSocket,
// } from '@nestjs/websockets';
// import { Server, Socket } from 'socket.io';

// Note: Uncomment the imports above when @nestjs/websockets is available

interface MockSocket {
  id: string;
  userId?: string;
  join(room: string): void;
  leave(room: string): void;
  emit(event: string, data: any): void;
  disconnect(): void;
}

interface MockServer {
  to(room: string): {
    emit(event: string, data: any): void;
  };
  emit(event: string, data: any): void;
}

// @WebSocketGateway({
//   cors: {
//     origin: process.env.FRONTEND_URL || 'http://localhost:5173',
//     credentials: true,
//   },
// })
export class WebSocketGateway {
  private readonly logger = new Logger(WebSocketGateway.name);
  
  // @WebSocketServer()
  server: MockServer;

  private connectedUsers: Map<string, MockSocket> = new Map();

  // OnGatewayConnection
  handleConnection(client: MockSocket) {
    this.logger.log(`Client connected: ${client.id}`);
  }

  // OnGatewayDisconnect
  handleDisconnect(client: MockSocket) {
    this.logger.log(`Client disconnected: ${client.id}`);
    
    // Remove from connected users
    for (const [userId, socket] of this.connectedUsers.entries()) {
      if (socket.id === client.id) {
        this.connectedUsers.delete(userId);
        break;
      }
    }
  }

  // @SubscribeMessage('authenticate')
  handleAuthentication(client: MockSocket, payload: { userId: string, token: string }) {
    try {
      // Validate JWT token here
      const { userId } = payload;
      
      // Store user connection
      this.connectedUsers.set(userId, client);
      client.userId = userId;
      
      // Join user-specific room
      client.join(`user:${userId}`);
      
      this.logger.log(`User authenticated: ${userId}`);
      
      client.emit('authenticated', { success: true });
    } catch (error) {
      this.logger.error('Authentication failed:', error);
      client.emit('authenticated', { success: false, error: 'Invalid token' });
    }
  }

  // @SubscribeMessage('join_conversation')
  handleJoinConversation(client: MockSocket, payload: { conversationId: string }) {
    const { conversationId } = payload;
    
    client.join(`conversation:${conversationId}`);
    
    this.logger.log(`User ${client.userId} joined conversation: ${conversationId}`);
    
    client.emit('joined_conversation', { conversationId });
  }

  // @SubscribeMessage('leave_conversation')
  handleLeaveConversation(client: MockSocket, payload: { conversationId: string }) {
    const { conversationId } = payload;
    
    client.leave(`conversation:${conversationId}`);
    
    this.logger.log(`User ${client.userId} left conversation: ${conversationId}`);
    
    client.emit('left_conversation', { conversationId });
  }

  // @SubscribeMessage('typing_start')
  handleTypingStart(client: MockSocket, payload: { conversationId: string }) {
    const { conversationId } = payload;
    
    client.to(`conversation:${conversationId}`).emit('user_typing', {
      userId: client.userId,
      conversationId,
      isTyping: true,
    });
  }

  // @SubscribeMessage('typing_stop')
  handleTypingStop(client: MockSocket, payload: { conversationId: string }) {
    const { conversationId } = payload;
    
    client.to(`conversation:${conversationId}`).emit('user_typing', {
      userId: client.userId,
      conversationId,
      isTyping: false,
    });
  }

  // @SubscribeMessage('mark_as_read')
  handleMarkAsRead(client: MockSocket, payload: { conversationId: string, messageId: string }) {
    const { conversationId, messageId } = payload;
    
    // Emit to other users in the conversation
    client.to(`conversation:${conversationId}`).emit('message_read', {
      userId: client.userId,
      conversationId,
      messageId,
      readAt: new Date(),
    });
  }

  // Public methods for emitting events from other services
  
  emitNewMessage(conversationId: string, message: any) {
    this.server.to(`conversation:${conversationId}`).emit('new_message', {
      conversationId,
      message,
    });
    
    this.logger.log(`Emitted new message to conversation: ${conversationId}`);
  }

  emitMessageStatusUpdate(conversationId: string, messageId: string, status: string) {
    this.server.to(`conversation:${conversationId}`).emit('message_status_update', {
      conversationId,
      messageId,
      status,
      updatedAt: new Date(),
    });
  }

  emitConversationUpdate(conversationId: string, updates: any) {
    this.server.to(`conversation:${conversationId}`).emit('conversation_update', {
      conversationId,
      updates,
      updatedAt: new Date(),
    });
  }

  emitUserNotification(userId: string, notification: any) {
    this.server.to(`user:${userId}`).emit('notification', {
      ...notification,
      timestamp: new Date(),
    });
  }

  emitChannelStatusUpdate(channelId: string, status: { isConnected: boolean, isHealthy: boolean }) {
    this.server.emit('channel_status_update', {
      channelId,
      status,
      updatedAt: new Date(),
    });
  }

  // Utility methods
  
  isUserConnected(userId: string): boolean {
    return this.connectedUsers.has(userId);
  }

  getConnectedUsers(): string[] {
    return Array.from(this.connectedUsers.keys());
  }

  disconnectUser(userId: string) {
    const socket = this.connectedUsers.get(userId);
    if (socket) {
      socket.disconnect();
      this.connectedUsers.delete(userId);
    }
  }

  broadcastToAllUsers(event: string, data: any) {
    this.server.emit(event, {
      ...data,
      timestamp: new Date(),
    });
  }

  getConnectionCount(): number {
    return this.connectedUsers.size;
  }
}
