import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type ConversationDocument = Conversation & Document;

@Schema({ timestamps: true })
export class Conversation {
  @Prop({ required: true })
  customerId: string;

  @Prop({ required: true })
  customerName: string;

  @Prop()
  customerEmail: string;

  @Prop()
  customerPhone: string;

  @Prop()
  customerAvatar: string;

  @Prop({ required: true, enum: ['facebook', 'zalo-personal', 'zalo-oa', 'email', 'sms'] })
  channel: string;

  @Prop({ required: true })
  channelConversationId: string;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  assignedTo: Types.ObjectId;

  @Prop({ enum: ['open', 'pending', 'resolved', 'closed'], default: 'open' })
  status: string;

  @Prop({ enum: ['low', 'medium', 'high', 'urgent'], default: 'medium' })
  priority: string;

  @Prop()
  subject: string;

  @Prop()
  lastMessageAt: Date;

  @Prop()
  lastMessagePreview: string;

  @Prop({ default: 0 })
  unreadCount: number;

  @Prop({ type: Object })
  channelData: Record<string, any>;

  @Prop([String])
  tags: string[];

  @Prop({ type: Object })
  metadata: Record<string, any>;
}

export const ConversationSchema = SchemaFactory.createForClass(Conversation);

// Create indexes for better query performance
ConversationSchema.index({ channel: 1, channelConversationId: 1 }, { unique: true });
ConversationSchema.index({ assignedTo: 1 });
ConversationSchema.index({ status: 1 });
ConversationSchema.index({ lastMessageAt: -1 });
ConversationSchema.index({ customerId: 1 });
