import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Conversation, ConversationDocument } from './schemas/conversation.schema';

@Injectable()
export class ConversationsService {
  constructor(
    @InjectModel(Conversation.name) private conversationModel: Model<ConversationDocument>,
  ) {}

  async create(createConversationDto: any): Promise<Conversation> {
    const createdConversation = new this.conversationModel(createConversationDto);
    return createdConversation.save();
  }

  async findAll(filters: any = {}): Promise<Conversation[]> {
    return this.conversationModel
      .find(filters)
      .populate('assignedTo', 'firstName lastName email')
      .sort({ lastMessageAt: -1 })
      .exec();
  }

  async findOne(id: string): Promise<Conversation> {
    return this.conversationModel
      .findById(id)
      .populate('assignedTo', 'firstName lastName email')
      .exec();
  }

  async findByChannelAndId(channel: string, channelConversationId: string): Promise<Conversation> {
    return this.conversationModel
      .findOne({ channel, channelConversationId })
      .populate('assignedTo', 'firstName lastName email')
      .exec();
  }

  async update(id: string, updateConversationDto: any): Promise<Conversation> {
    return this.conversationModel
      .findByIdAndUpdate(id, updateConversationDto, { new: true })
      .populate('assignedTo', 'firstName lastName email')
      .exec();
  }

  async updateLastMessage(id: string, lastMessagePreview: string): Promise<Conversation> {
    return this.conversationModel
      .findByIdAndUpdate(
        id,
        {
          lastMessageAt: new Date(),
          lastMessagePreview,
          $inc: { unreadCount: 1 },
        },
        { new: true },
      )
      .exec();
  }

  async markAsRead(id: string): Promise<Conversation> {
    return this.conversationModel
      .findByIdAndUpdate(id, { unreadCount: 0 }, { new: true })
      .exec();
  }

  async remove(id: string): Promise<Conversation> {
    return this.conversationModel.findByIdAndDelete(id).exec();
  }
}
